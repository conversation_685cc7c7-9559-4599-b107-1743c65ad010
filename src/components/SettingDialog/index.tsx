import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Dropdown,
  Dropdown<PERSON><PERSON>ger,
  DropdownMenu,
  DropdownItem,
  Switch,
} from '@heroui/react'
import { useTheme } from '@heroui/use-theme'
import { useStore } from '@/store'
import { observer } from 'mobx-react-lite'

// 图标组件
const LanguageIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z"
      fill="currentColor"
    />
  </svg>
)

const MoonIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17.75 4.09L15.22 6.03L16.13 9.09L13.5 7.28L10.87 9.09L11.78 6.03L9.25 4.09L12.44 4L13.5 1L14.56 4L17.75 4.09ZM21.25 11L19.61 12.25L20.2 14.23L18.5 13.06L16.8 14.23L17.39 12.25L15.75 11L17.81 10.95L18.5 9L19.19 10.95L21.25 11ZM18.97 15.95C19.8 15.87 20.69 17.05 20.16 17.8C19.84 18.25 19.5 18.67 19.08 19.07C15.17 23 8.84 23 4.94 19.07C1.03 15.17 1.03 8.83 4.94 4.93C5.34 4.53 5.76 4.17 6.21 3.85C6.96 3.32 8.14 4.21 8.06 5.04C7.79 7.9 8.75 10.87 10.95 13.06C13.14 15.26 16.1 16.22 18.97 15.95Z"
      fill="currentColor"
    />
  </svg>
)

const HelpIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 19H11V17H13V19ZM15.07 11.25L14.17 12.17C13.45 12.9 13 13.5 13 15H11V14.5C11 13.4 11.45 12.4 12.17 11.67L13.41 10.41C13.78 10.05 14 9.55 14 9C14 7.9 13.1 7 12 7C10.9 7 10 7.9 10 9H8C8 6.79 9.79 5 12 5C14.21 5 16 6.79 16 9C16 9.88 15.64 10.68 15.07 11.25Z"
      fill="currentColor"
    />
  </svg>
)

const ChevronDownIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z" fill="currentColor" />
  </svg>
)

const CloseIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"
      fill="currentColor"
    />
  </svg>
)

export interface SettingDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export const SettingDialog = observer(({ isOpen, onOpenChange }: SettingDialogProps) => {
  const { theme, setTheme } = useTheme()
  const { lang } = useStore()
  const [isAfqExpanded, setIsAfqExpanded] = useState(false)

  // 语言选项
  const languageOptions = [
    { key: 'en', label: 'English' },
    { key: 'zh_CN', label: '中文' },
    { key: 'ko', label: '한국어' },
    { key: 'fr', label: 'Français' },
    { key: 'de', label: 'Deutsch' },
  ]

  const currentLanguage = languageOptions.find(option => option.key === lang.lang) || languageOptions[0]

  const handleLanguageChange = async (keys: any) => {
    const selectedKey = Array.from(keys)[0] as string
    await lang.setLang(selectedKey)
  }

  const handleThemeToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  return (
    <Modal
      hideCloseButton
      classNames={{
        base: 'bg-[#2D2B3A] border border-[#3A3847] rounded-2xl max-w-[400px]',
        header: 'border-b-0 pb-2',
        body: 'py-4',
      }}
      isOpen={isOpen}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -10,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
      size="sm"
      onOpenChange={onOpenChange}
    >
      <ModalContent>
        {onClose => (
          <>
            <ModalHeader className="flex justify-between items-center px-6 pt-6">
              <h2 className="text-xl font-medium text-white">Setting</h2>
              <Button
                isIconOnly
                variant="light"
                onPress={onClose}
                className="text-white/70 hover:text-white hover:bg-white/10 min-w-6 h-6"
              >
                <CloseIcon />
              </Button>
            </ModalHeader>
            <ModalBody className="px-6 pb-6">
              <div className="space-y-6">
                {/* Language Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <LanguageIcon />
                    <span className="text-white text-base">Language</span>
                  </div>
                  <Dropdown>
                    <DropdownTrigger>
                      <Button
                        variant="flat"
                        className="bg-[#3A3847] text-white border-none hover:bg-[#4A4857] min-w-[120px] justify-between"
                        endContent={<ChevronDownIcon />}
                      >
                        {currentLanguage.label}
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      aria-label="Language selection"
                      selectedKeys={new Set([lang.lang])}
                      selectionMode="single"
                      onSelectionChange={handleLanguageChange}
                      classNames={{
                        base: 'bg-[#2D2B3A] border border-[#3A3847]',
                        list: 'bg-[#2D2B3A]',
                      }}
                    >
                      {languageOptions.map(option => (
                        <DropdownItem
                          key={option.key}
                          className="text-white hover:bg-[#3A3847] data-[selected=true]:bg-[#8F7EFF]/20 data-[selected=true]:text-[#8F7EFF]"
                        >
                          {option.label}
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                </div>

                {/* Dark Mode Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <MoonIcon />
                    <span className="text-white text-base">Dark mode</span>
                  </div>
                  <Switch
                    isSelected={theme === 'dark'}
                    onValueChange={handleThemeToggle}
                    classNames={{
                      base: 'inline-flex flex-row-reverse w-auto bg-transparent hover:bg-transparent',
                      wrapper: 'p-0 h-6 w-12 bg-[#3A3847] group-data-[selected=true]:bg-[#8F7EFF] rounded-full',
                      thumb:
                        'w-5 h-5 border-0 shadow-lg bg-white rounded-full group-data-[selected=true]:ml-6 ml-0.5 transition-all',
                    }}
                  />
                </div>

                {/* AFQ Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <HelpIcon />
                    <span className="text-white text-base">AFQ</span>
                  </div>
                  <Button
                    variant="light"
                    isIconOnly
                    onPress={() => setIsAfqExpanded(!isAfqExpanded)}
                    className={`text-white/70 hover:text-white hover:bg-white/10 min-w-6 h-6 transition-transform ${
                      isAfqExpanded ? 'rotate-180' : ''
                    }`}
                  >
                    <ChevronDownIcon />
                  </Button>
                </div>

                {/* AFQ Expanded Content */}
                {isAfqExpanded && (
                  <div className="ml-8 space-y-3 text-white/80 text-sm">
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">How to bridge tokens?</p>
                      <p className="text-white/60">
                        Connect your wallet, select networks and tokens, then confirm the transaction.
                      </p>
                    </div>
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">What are the fees?</p>
                      <p className="text-white/60">Bridge fees vary by network. Gas fees are paid separately.</p>
                    </div>
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">How long does bridging take?</p>
                      <p className="text-white/60">Usually 5-30 minutes depending on network congestion.</p>
                    </div>
                  </div>
                )}
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  )
})
