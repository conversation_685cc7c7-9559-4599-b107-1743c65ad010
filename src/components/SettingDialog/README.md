# SettingDialog Component

一个功能完整的设置对话框组件，提供语言切换、主题切换和常见问题展示功能。

## 功能特性

- 🌍 **语言切换**: 支持多种语言（英语、中文、韩语、法语、德语）
- 🌙 **主题切换**: 明暗主题切换，集成项目的主题系统
- ❓ **常见问题**: 可展开的AFQ部分，提供常用问题解答
- 🎨 **美观设计**: 符合设计规范的深色主题界面
- 📱 **响应式**: 适配不同屏幕尺寸

## 使用方法

### 基础用法

```tsx
import { SettingDialog } from '@/components/SettingDialog';
import { useDisclosure } from '@heroui/react';

function MyComponent() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  return (
    <>
      <Button onPress={onOpen}>打开设置</Button>
      <SettingDialog 
        isOpen={isOpen} 
        onOpenChange={onOpenChange} 
      />
    </>
  );
}
```

### Props

| 属性 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `isOpen` | `boolean` | ✅ | 控制对话框的显示/隐藏状态 |
| `onOpenChange` | `(open: boolean) => void` | ✅ | 对话框状态变化时的回调函数 |

## 组件结构

```
SettingDialog/
├── index.tsx          # 主组件文件
├── SettingDialog.test.tsx  # 测试文件
└── README.md          # 说明文档
```

## 依赖项

- `@heroui/react` - UI组件库
- `@heroui/use-theme` - 主题管理
- `mobx-react-lite` - 状态管理
- `@/store` - 项目状态管理

## 样式特点

- 深色主题背景 (`#2D2B3A`)
- 圆角设计 (`rounded-2xl`)
- 紫色主题色 (`#8F7EFF`)
- 平滑的动画过渡
- 响应式布局

## 功能详解

### 语言切换
- 支持5种语言：英语、中文、韩语、法语、德语
- 与项目的语言管理系统集成
- 选择后立即生效并保存到本地存储

### 主题切换
- 明暗主题切换
- 紫色开关设计
- 与项目的主题系统同步

### 常见问题 (AFQ)
- 可展开/收起的问题列表
- 包含桥接相关的常见问题
- 左侧紫色边框设计

## 自定义

如需自定义样式，可以修改组件中的 Tailwind CSS 类名：

```tsx
// 修改背景色
classNames={{
  base: "bg-[#YOUR_COLOR] border border-[#YOUR_BORDER_COLOR]",
}}

// 修改主题色
className="bg-[#YOUR_THEME_COLOR]"
```

## 测试

运行测试：

```bash
npm test SettingDialog.test.tsx
```

测试覆盖：
- 组件渲染
- 交互功能
- 状态管理
- 事件处理

## 注意事项

1. 确保项目已正确配置 HeroUI 和主题系统
2. 语言文件需要放在 `public/translations/` 目录下
3. 组件依赖 MobX 状态管理，确保在 Provider 内使用
